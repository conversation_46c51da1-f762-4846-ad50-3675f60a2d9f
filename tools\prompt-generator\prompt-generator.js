// --- PROMPT GENERATOR LOGIC ---
(function() {
    // Initialize the prompt generator when loaded
    initializePromptGenerator();

    function initializePromptGenerator() {
        const exportButton = document.getElementById('export-button');
        if (exportButton) {
            exportButton.addEventListener('click', exportPrompts);
        }
    }

    function exportPrompts() {
        const positiveText = document.getElementById('positive-prompts').value.trim();
        const negativeText = document.getElementById('negative-prompts').value.trim();
        const applyAllNegative = document.getElementById('apply-all-negative').checked;

        if (!positiveText) {
            alert('Please enter at least one positive prompt.');
            return;
        }

        // Split prompts by blank lines (double newlines)
        const positivePrompts = positiveText.split(/\n\s*\n/).filter(prompt => prompt.trim());
        const negativePrompts = negativeText.split(/\n\s*\n/).filter(prompt => prompt.trim());

        let output = '';
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);

        // Generate formatted output
        positivePrompts.forEach((positivePrompt, index) => {
            const cleanPositive = positivePrompt.replace(/\n/g, ' ').trim();
            
            let negativePrompt = '';
            if (applyAllNegative && negativePrompts.length > 0) {
                // Use the first negative prompt for all
                negativePrompt = negativePrompts[0].replace(/\n/g, ' ').trim();
            } else if (negativePrompts[index]) {
                // Use corresponding negative prompt
                negativePrompt = negativePrompts[index].replace(/\n/g, ' ').trim();
            }

            output += `Positive: ${cleanPositive}\n`;
            if (negativePrompt) {
                output += `Negative: ${negativePrompt}\n`;
            }
            output += '\n'; // Add blank line between prompt sets
        });

        // Create and download file
        const blob = new Blob([output], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `generate.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // Show success message
        const originalText = exportButton.textContent;
        exportButton.textContent = '✓ Exported!';
        exportButton.style.backgroundColor = 'var(--success-color)';
        
        setTimeout(() => {
            exportButton.textContent = originalText;
            exportButton.style.backgroundColor = '';
        }, 2000);
    }
})();

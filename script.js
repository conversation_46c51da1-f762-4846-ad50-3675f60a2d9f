// Wait for the DOM to be fully loaded before running scripts
document.addEventListener('DOMContentLoaded', function() {
    
    // --- Initialize Dashboard ---
    initializeTheme();
    initializeNavigation();
    
    // Load the default tool (prompt-generator)
    loadTool('prompt-generator');

});

// --- MODULAR DASHBOARD NAVIGATION LOGIC ---
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.sidebar-nav a');

    navLinks.forEach(link => {
        link.addEventListener('click', function(event) {
            event.preventDefault();
            const targetTool = this.getAttribute('data-target');
            
            // Update active navigation state
            navLinks.forEach(navLink => navLink.parentElement.classList.remove('active'));
            this.parentElement.classList.add('active');
            
            // Load the selected tool
            loadTool(targetTool);
        });
    });
}

// --- TOOL TEMPLATES (Auto-generated from individual tool files) ---
const TOOL_TEMPLATES = {
    'aspect-ratio-calculator': {
        html: `<div class="dashboard-view active">
    <h2>Aspect Ratio Calculator</h2>
    <p class="view-description">Calculate dimensions using presets or find the ratio of custom sizes.</p>

    <div class="tool-section">
        <h3>Preset Calculator</h3>
        <div class="calculator-grid">
            <div class="form-group">
                <label for="aspect-ratio-select">Aspect Ratio</label>
                <select id="aspect-ratio-select">
                    <option value="1/1">1:1 (Square)</option>
                    <option value="4/3">4:3 (Standard)</option>
                    <option value="3/2">3:2 (Photography)</option>
                    <option value="16/9" selected>16:9 (Widescreen)</option>
                    <option value="9/16">9:16 (Vertical Video)</option>
                    <option value="21/9">21:9 (Ultrawide)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="preset-width">Width</label>
                <input type="number" id="preset-width" placeholder="e.g., 1920">
            </div>
            <div class="form-group">
                <label for="preset-height">Height</label>
                <input type="number" id="preset-height" placeholder="e.g., 1080">
            </div>
        </div>
    </div>

    <div class="tool-section">
        <h3>Custom Ratio Finder</h3>
        <div class="calculator-grid">
            <div class="form-group">
                <label for="custom-width">Width</label>
                <input type="number" id="custom-width" placeholder="Enter width">
            </div>
            <div class="form-group">
                <label for="custom-height">Height</label>
                <input type="number" id="custom-height" placeholder="Enter height">
            </div>
             <div class="form-group button-group">
                <button id="calculate-custom-ratio" class="action-button">Calculate Ratio</button>
            </div>
        </div>
        <div id="custom-ratio-result" class="result-box">
            Your calculated ratio will appear here.
        </div>
    </div>
</div>`,
        css: `/* --- Aspect Ratio Calculator Styles --- */
.tool-section {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.tool-section h3 {
    margin-bottom: 1.5rem;
    color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
}

.calculator-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color);
}

.form-group.button-group {
    justify-content: flex-end;
}

input[type="number"], select {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
}

input[type="number"]:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.result-box {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: var(--bg-color);
    border-radius: 8px;
    text-align: center;
    font-size: 1.1rem;
    color: var(--subtle-text-color);
    transition: all 0.3s;
}

.result-box.success {
    color: var(--success-color);
    font-weight: 500;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    .calculator-grid {
        grid-template-columns: 1fr;
    }
}`,
        init: function() {
            initializeAspectRatioCalculator();

    function initializeAspectRatioCalculator() {

        const select = document.getElementById('aspect-ratio-select');
        const presetWidthInput = document.getElementById('preset-width');
        const presetHeightInput = document.getElementById('preset-height');

        const customWidthInput = document.getElementById('custom-width');
        const customHeightInput = document.getElementById('custom-height');
        const calculateBtn = document.getElementById('calculate-custom-ratio');
        const resultBox = document.getElementById('custom-ratio-result');

        select?.addEventListener('change', () => handlePresetCalculation('width'));
        presetWidthInput?.addEventListener('input', () => handlePresetCalculation('width'));
        presetHeightInput?.addEventListener('input', () => handlePresetCalculation('height'));

        calculateBtn?.addEventListener('click', calculateCustomRatio);


        function handlePresetCalculation(changedInput) {
            const ratio = select.value.split('/').map(Number); // e.g., [16, 9]
            const width = parseFloat(presetWidthInput.value);
            const height = parseFloat(presetHeightInput.value);

            if (changedInput === 'width' && width > 0) {
                const newHeight = (width / ratio[0]) * ratio[1];
                presetHeightInput.value = Math.round(newHeight);
            } else if (changedInput === 'height' && height > 0) {
                const newWidth = (height / ratio[1]) * ratio[0];
                presetWidthInput.value = Math.round(newWidth);
            }
        }

        function calculateCustomRatio() {
            const width = parseInt(customWidthInput.value);
            const height = parseInt(customHeightInput.value);

            if (!width || !height || width <= 0 || height <= 0) {
                resultBox.textContent = "Please enter valid width and height.";
                resultBox.classList.remove('success');
                return;
            }

            const divisor = gcd(width, height);
            const simplifiedWidth = width / divisor;
            const simplifiedHeight = height / divisor;
            const decimalRatio = (width / height).toFixed(3);

            resultBox.innerHTML = `Simplified: <strong>${simplifiedWidth}:${simplifiedHeight}</strong> (Decimal: ${decimalRatio})`;
            resultBox.classList.add('success');
        }

        function gcd(a, b) {
            while (b) {
                [a, b] = [b, a % b];
            }
            return a;
        }
    }
        }
    },

    'prompt-generator': {
        html: `<div class="dashboard-view active">
    <h2>Prompt Generator (For Inspire-pack)</h2>
    <p class="view-description">Automated format generator specifically for Inspire-pack. Separate prompts with a blank line to generate your formatted text file.</p>
    <div class="prompt-box-wrapper">
        <div class="prompt-box">
            <label for="positive-prompts">✅ Positive Prompts</label>
            <textarea id="positive-prompts" placeholder="A stunning portrait...&#10;&#10;A beautiful landscape..."></textarea>
        </div>
        <div class="prompt-box">
            <label for="negative-prompts">❌ Negative Prompts</label>
            <textarea id="negative-prompts" placeholder="blurry, ugly..."></textarea>
        </div>
    </div>
    <div class="options-container">
        <label class="toggle-switch">
            <input type="checkbox" id="apply-all-negative">
            <span class="slider"></span>
        </label>
        <label for="apply-all-negative">Use the first negative prompt for all</label>
    </div>
    <button id="export-button" class="action-button">Export to .txt</button>
</div>`,
        css: `/* --- Prompt Generator Styles --- */
.prompt-box-wrapper {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.prompt-box {
    flex: 1;
}

.prompt-box label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 500;
    font-size: 1rem;
}

textarea {
    width: 100%;
    height: 350px;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    font-family: 'Poppins', sans-serif;
    resize: vertical;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: all 0.3s;
}

textarea::placeholder {
    color: var(--subtle-text-color);
}

textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.options-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 2rem 0;
    padding: 1rem;
    background-color: var(--bg-color);
    border-radius: 8px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 28px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--subtle-text-color);
    transition: .4s;
    border-radius: 28px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(22px);
}

.options-container label {
    margin-left: 12px;
    cursor: pointer;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    .prompt-box-wrapper {
        flex-direction: column;
    }
}`,
        init: function() {
            initializePromptGenerator();

    function initializePromptGenerator() {
        const exportButton = document.getElementById('export-button');
        if (exportButton) {
            exportButton.addEventListener('click', exportPrompts);
        }
    }

    function exportPrompts() {
        const positiveText = document.getElementById('positive-prompts').value.trim();
        const negativeText = document.getElementById('negative-prompts').value.trim();
        const applyAllNegative = document.getElementById('apply-all-negative').checked;

        if (!positiveText) {
            alert('Please enter at least one positive prompt.');
            return;
        }

        const positivePrompts = positiveText.split(/\n\s*\n/).filter(prompt => prompt.trim());
        const negativePrompts = negativeText.split(/\n\s*\n/).filter(prompt => prompt.trim());

        let output = '';
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);

        positivePrompts.forEach((positivePrompt, index) => {
            const cleanPositive = positivePrompt.replace(/\n/g, ' ').trim();

            let negativePrompt = '';
            if (applyAllNegative && negativePrompts.length > 0) {

                negativePrompt = negativePrompts[0].replace(/\n/g, ' ').trim();
            } else if (negativePrompts[index]) {

                negativePrompt = negativePrompts[index].replace(/\n/g, ' ').trim();
            }

            output += `positive:${cleanPositive}\n\n`;
            output += `negative:${negativePrompt}\n`;
            output += '-----------------\n';
        });

        const blob = new Blob([output], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `generate.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        const originalText = exportButton.textContent;
        exportButton.textContent = '✓ Exported!';
        exportButton.style.backgroundColor = 'var(--success-color)';
        
        setTimeout(() => {
            exportButton.textContent = originalText;
            exportButton.style.backgroundColor = '';
        }, 2000);
    }
        }
    },

    'settings': {
        html: `<div class="dashboard-view active">
    <h2>Settings</h2>
    <p class="view-description">Configure your dashboard preferences and tool settings.</p>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="5"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
            Appearance
        </h3>
        <div class="setting-group">
            <label for="theme-select" class="setting-label">Theme</label>
            <p class="setting-description">Choose your preferred color theme for the dashboard</p>
            <select id="theme-select" class="setting-select">
                <option value="dark">Dark (Default)</option>
                <option value="light">Light</option>
                <option value="ocean">Ocean</option>
                <option value="forest">Forest</option>
                <option value="purple">Purple</option>
            </select>
        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Preferences
        </h3>
        <div class="setting-group">
            <label class="setting-checkbox">
                <input type="checkbox" id="auto-save-settings">
                <span class="checkmark"></span>
                <div class="checkbox-content">
                    <span class="checkbox-label">Auto-save settings</span>
                    <span class="checkbox-description">Automatically save your preferences</span>
                </div>
            </label>
        </div>
        <div class="setting-group">
            <label class="setting-checkbox">
                <input type="checkbox" id="show-tooltips" checked>
                <span class="checkmark"></span>
                <div class="checkbox-content">
                    <span class="checkbox-label">Show tooltips</span>
                    <span class="checkbox-description">Display helpful tooltips throughout the interface</span>
                </div>
            </label>
        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"/>
                <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"/>
            </svg>
            Actions
        </h3>
        <div class="setting-group">
            <button id="reset-settings" class="action-button secondary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M1 4v6h6"/>
                    <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                </svg>
                Reset to Defaults
            </button>
        </div>
    </div>

    <div class="settings-footer">
        <p class="settings-info">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 16v-4"/>
                <path d="M12 8h.01"/>
            </svg>
            Settings are automatically saved to your browser's local storage
        </p>
    </div>
</div>`,
        css: `/* Settings Tool Styles */
.settings-section {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.settings-section h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    font-size: 1.1rem;
}

.section-icon {
    color: var(--primary-color);
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.setting-description {
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.setting-select {
    width: 100%;
    max-width: 300px;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
    cursor: pointer;
}

.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

/* Custom Checkbox Styles */
.setting-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 8px;
    transition: background-color 0.3s;
}

.setting-checkbox:hover {
    background-color: rgba(122, 162, 247, 0.05);
}

.setting-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s;
    flex-shrink: 0;
    margin-top: 2px;
}

.setting-checkbox input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.setting-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-content {
    flex: 1;
}

.checkbox-label {
    display: block;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.checkbox-description {
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    line-height: 1.4;
}

/* Secondary Action Button */
.action-button.secondary {
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    width: auto;
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
}

.action-button.secondary:hover {
    background-color: var(--border-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Settings Footer */
.settings-footer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.settings-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    margin: 0;
}

.settings-info svg {
    color: var(--primary-color);
    flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-section {
        padding: 1.5rem;
    }
    
    .setting-checkbox {
        padding: 0.5rem;
    }
}`,
        init: function() {
            initializeSettings();

    function initializeSettings() {

        loadSettings();

        setupEventListeners();
        
        console.log('Settings tool loaded');
    }

    function setupEventListeners() {

        const themeSelect = document.getElementById('theme-select');
        if (themeSelect) {
            themeSelect.addEventListener('change', handleThemeChange);
        }

        const autoSaveCheckbox = document.getElementById('auto-save-settings');
        if (autoSaveCheckbox) {
            autoSaveCheckbox.addEventListener('change', handleAutoSaveChange);
        }

        const tooltipsCheckbox = document.getElementById('show-tooltips');
        if (tooltipsCheckbox) {
            tooltipsCheckbox.addEventListener('change', handleTooltipsChange);
        }

        const resetButton = document.getElementById('reset-settings');
        if (resetButton) {
            resetButton.addEventListener('click', handleResetSettings);
        }
    }

    function handleThemeChange(event) {
        const selectedTheme = event.target.value;
        applyTheme(selectedTheme);
        saveSettings();

        showSettingsFeedback('Theme changed successfully!');
    }

    function applyTheme(theme) {

        if (theme === 'dark') {
            document.documentElement.removeAttribute('data-theme');
        } else {
            document.documentElement.setAttribute('data-theme', theme);
        }
    }

    function handleAutoSaveChange(event) {
        const isEnabled = event.target.checked;
        saveSettings();
        showSettingsFeedback(`Auto-save ${isEnabled ? 'enabled' : 'disabled'}`);
    }

    function handleTooltipsChange(event) {
        const isEnabled = event.target.checked;
        saveSettings();
        showSettingsFeedback(`Tooltips ${isEnabled ? 'enabled' : 'disabled'}`);
    }

    function handleResetSettings() {
        if (confirm('Are you sure you want to reset all settings to their defaults? This action cannot be undone.')) {

            const defaults = {
                theme: 'dark',
                autoSave: false,
                showTooltips: true
            };

            document.getElementById('theme-select').value = defaults.theme;
            document.getElementById('auto-save-settings').checked = defaults.autoSave;
            document.getElementById('show-tooltips').checked = defaults.showTooltips;

            applyTheme(defaults.theme);

            localStorage.setItem('comfyui-helper-settings', JSON.stringify(defaults));
            
            showSettingsFeedback('Settings reset to defaults');
        }
    }

    function saveSettings() {
        const settings = {
            theme: document.getElementById('theme-select')?.value || 'dark',
            autoSave: document.getElementById('auto-save-settings')?.checked || false,
            showTooltips: document.getElementById('show-tooltips')?.checked || true
        };
        
        localStorage.setItem('comfyui-helper-settings', JSON.stringify(settings));
    }

    function loadSettings() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);

                if (settings.theme) {
                    const themeSelect = document.getElementById('theme-select');
                    if (themeSelect) {
                        themeSelect.value = settings.theme;
                        applyTheme(settings.theme);
                    }
                }

                const autoSaveCheckbox = document.getElementById('auto-save-settings');
                if (autoSaveCheckbox && typeof settings.autoSave === 'boolean') {
                    autoSaveCheckbox.checked = settings.autoSave;
                }
                
                const tooltipsCheckbox = document.getElementById('show-tooltips');
                if (tooltipsCheckbox && typeof settings.showTooltips === 'boolean') {
                    tooltipsCheckbox.checked = settings.showTooltips;
                }
            }
        } catch (error) {
            console.warn('Failed to load settings:', error);
        }
    }

    function showSettingsFeedback(message) {

        let feedback = document.querySelector('.settings-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'settings-feedback';
            feedback.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: var(--success-color);
                color: white;
                padding: 0.75rem 1rem;
                border-radius: 8px;
                font-size: 0.9rem;
                font-weight: 500;
                z-index: 1000;
                opacity: 0;
                transform: translateY(-10px);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(feedback);
        }
        
        feedback.textContent = message;

        requestAnimationFrame(() => {
            feedback.style.opacity = '1';
            feedback.style.transform = 'translateY(0)';
        });

        setTimeout(() => {
            feedback.style.opacity = '0';
            feedback.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 300);
        }, 3000);
    }

    function initializeThemeOnLoad() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                if (settings.theme) {
                    applyTheme(settings.theme);
                }
            }
        } catch (error) {
            console.warn('Failed to initialize theme:', error);
        }
    }

    initializeThemeOnLoad();
        }
    },

};


// --- TOOL LOADING SYSTEM (No Server Required) ---
async function loadTool(toolName) {
    const contentArea = document.getElementById('tool-content');
    
    try {
        // Show loading state
        contentArea.innerHTML = '<div class="loading">Loading...</div>';
        
        // Get tool template
        const toolTemplate = TOOL_TEMPLATES[toolName];
        if (!toolTemplate) {
            throw new Error(`Tool ${toolName} not found`);
        }
        
        // Load HTML content
        contentArea.innerHTML = toolTemplate.html;
        
        // Load CSS
        loadToolCSS(toolName, toolTemplate.css);
        
        // Initialize tool functionality
        if (toolTemplate.init) {
            toolTemplate.init();
        }
        
    } catch (error) {
        console.error(`Error loading tool ${toolName}:`, error);
        contentArea.innerHTML = `<div class="error">Failed to load ${toolName}. Please try again.</div>`;
    }
}

// Load tool-specific CSS
function loadToolCSS(toolName, cssContent) {
    const cssId = `${toolName}-css`;
    
    // Remove existing tool CSS
    const existingCSS = document.getElementById(cssId);
    if (existingCSS) {
        existingCSS.remove();
    }
    
    // Add new CSS
    const style = document.createElement('style');
    style.id = cssId;
    style.textContent = cssContent;
    document.head.appendChild(style);
}

// --- THEME INITIALIZATION ---
function initializeTheme() {
    try {
        const savedSettings = localStorage.getItem('comfyui-helper-settings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            if (settings.theme && settings.theme !== 'dark') {
                document.documentElement.setAttribute('data-theme', settings.theme);
            }
        }
    } catch (error) {
        console.warn('Failed to initialize theme:', error);
    }
}
